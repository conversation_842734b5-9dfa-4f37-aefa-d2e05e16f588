using Microsoft.Extensions.Logging;
using TenantDB.Core.DataLayer;
using TenantDB.Core.QueryObjects;
using TenantDB.Core.QueryObjects.DatabaseQueries;

namespace TenantDB.Core.Services;

/// <summary>
/// Service for setting up tenant databases with the complete schema
/// </summary>
public class TenantDatabaseSetupService : ITenantDatabaseSetupService
{
    private readonly IDataLayer _dataLayer;
    private readonly IQueryExecutor _queryExecutor;
    private readonly ILogger<TenantDatabaseSetupService> _logger;
    private readonly string _sqlScriptsPath;

    public TenantDatabaseSetupService(
        IDataLayer dataLayer,
        IQueryExecutor queryExecutor,
        ILogger<TenantDatabaseSetupService> logger,
        string sqlScriptsPath = "sql")
    {
        _dataLayer = dataLayer ?? throw new ArgumentNullException(nameof(dataLayer));
        _queryExecutor = queryExecutor ?? throw new ArgumentNullException(nameof(queryExecutor));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _sqlScriptsPath = sqlScriptsPath;
    }

    /// <inheritdoc />
    public async Task<bool> SetupTenantDatabaseAsync(string tenantId, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(tenantId))
            throw new ArgumentException("Tenant ID cannot be null or empty", nameof(tenantId));

        var databaseName = GetTenantDatabaseName(tenantId);
        
        try
        {
            _logger.LogInformation("Starting database setup for tenant {TenantId} (database: {DatabaseName})", tenantId, databaseName);

            // Check if database already exists
            if (await TenantDatabaseExistsAsync(tenantId, cancellationToken))
            {
                _logger.LogWarning("Database {DatabaseName} already exists for tenant {TenantId}", databaseName, tenantId);
                return false;
            }

            // Create the database
            var createCommand = new CreateDatabaseCommand(databaseName);
            var result = await _queryExecutor.ExecuteCommandAsync(createCommand, "postgres");

            // PostgreSQL returns -1 for DDL commands like CREATE DATABASE, which is normal
            _logger.LogDebug("Create database command returned: {Result}", result);

            _logger.LogInformation("Database {DatabaseName} created successfully for tenant {TenantId}", databaseName, tenantId);

            // Execute SQL scripts to set up the schema
            await ExecuteSqlScriptsAsync(databaseName, cancellationToken);

            _logger.LogInformation("Database setup completed successfully for tenant {TenantId}", tenantId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to setup database for tenant {TenantId}", tenantId);
            
            // Attempt cleanup if database was created but schema setup failed
            try
            {
                if (await TenantDatabaseExistsAsync(tenantId, cancellationToken))
                {
                    await RemoveTenantDatabaseAsync(tenantId, cancellationToken);
                }
            }
            catch (Exception cleanupEx)
            {
                _logger.LogError(cleanupEx, "Failed to cleanup database after setup failure for tenant {TenantId}", tenantId);
            }
            
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<bool> TenantDatabaseExistsAsync(string tenantId, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(tenantId))
            throw new ArgumentException("Tenant ID cannot be null or empty", nameof(tenantId));

        var databaseName = GetTenantDatabaseName(tenantId);
        var query = new CheckDatabaseExistsQuery(databaseName);
        
        try
        {
            var exists = await _queryExecutor.ExecuteScalarAsync(query, "postgres");
            return exists == true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check if database exists for tenant {TenantId}", tenantId);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<bool> RemoveTenantDatabaseAsync(string tenantId, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(tenantId))
            throw new ArgumentException("Tenant ID cannot be null or empty", nameof(tenantId));

        var databaseName = GetTenantDatabaseName(tenantId);
        
        try
        {
            _logger.LogWarning("Removing database {DatabaseName} for tenant {TenantId}", databaseName, tenantId);
            var result = await _dataLayer.DropDatabaseAsync(databaseName);
            
            if (result)
            {
                _logger.LogInformation("Database {DatabaseName} removed successfully for tenant {TenantId}", databaseName, tenantId);
            }
            else
            {
                _logger.LogError("Failed to remove database {DatabaseName} for tenant {TenantId}", databaseName, tenantId);
            }
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to remove database for tenant {TenantId}", tenantId);
            return false;
        }
    }

    /// <inheritdoc />
    public string GetTenantDatabaseName(string tenantId)
    {
        if (string.IsNullOrWhiteSpace(tenantId))
            throw new ArgumentException("Tenant ID cannot be null or empty", nameof(tenantId));

        // Create a safe database name from tenant ID
        var safeTenantId = tenantId.ToLowerInvariant()
            .Replace("-", "_")
            .Replace(" ", "_");
            
        return $"tenant_{safeTenantId}";
    }

    private async Task ExecuteSqlScriptsAsync(string databaseName, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Executing SQL scripts for database {DatabaseName}", databaseName);

        // Get all SQL files in order
        var sqlFiles = GetSqlFilesInOrder();
        
        foreach (var sqlFile in sqlFiles)
        {
            cancellationToken.ThrowIfCancellationRequested();
            
            _logger.LogDebug("Executing SQL script: {SqlFile}", sqlFile);
            
            var sqlContent = await File.ReadAllTextAsync(sqlFile, cancellationToken);

            // Execute the entire SQL file content at once
            try
            {
                await _dataLayer.ExecuteNonQueryAsync(sqlContent, null, databaseName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to execute SQL script {SqlFile}", sqlFile);
                throw;
            }
            
            _logger.LogDebug("Successfully executed SQL script: {SqlFile}", sqlFile);
        }
        
        _logger.LogInformation("All SQL scripts executed successfully for database {DatabaseName}", databaseName);
    }

    private string[] GetSqlFilesInOrder()
    {
        if (!Directory.Exists(_sqlScriptsPath))
        {
            throw new DirectoryNotFoundException($"SQL scripts directory not found: {_sqlScriptsPath}");
        }

        var sqlFiles = Directory.GetFiles(_sqlScriptsPath, "*.sql")
            .Where(f => !Path.GetFileName(f).Equals("init.sql", StringComparison.OrdinalIgnoreCase))
            .OrderBy(f => Path.GetFileName(f))
            .ToArray();

        if (sqlFiles.Length == 0)
        {
            throw new InvalidOperationException($"No SQL files found in directory: {_sqlScriptsPath}");
        }

        _logger.LogDebug("Found {Count} SQL files to execute: {Files}", sqlFiles.Length, string.Join(", ", sqlFiles.Select(Path.GetFileName)));
        
        return sqlFiles;
    }


}
