{"version": 2, "dgSpecHash": "xz03CFz6Vcc=", "success": true, "projectFilePath": "/home/<USER>/projects/whatson2/tenantdb/TenantDB.Core/TenantDB.Core.csproj", "expectedPackageFiles": ["/home/<USER>/.nuget/packages/dapper/2.1.66/dapper.2.1.66.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.configuration/9.0.8/microsoft.extensions.configuration.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.configuration.abstractions/9.0.8/microsoft.extensions.configuration.abstractions.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.configuration.binder/9.0.8/microsoft.extensions.configuration.binder.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection.abstractions/9.0.7/microsoft.extensions.dependencyinjection.abstractions.9.0.7.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.logging.abstractions/9.0.7/microsoft.extensions.logging.abstractions.9.0.7.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.primitives/9.0.8/microsoft.extensions.primitives.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/npgsql/9.0.3/npgsql.9.0.3.nupkg.sha512", "/home/<USER>/.nuget/packages/system.text.json/9.0.7/system.text.json.9.0.7.nupkg.sha512"], "logs": []}