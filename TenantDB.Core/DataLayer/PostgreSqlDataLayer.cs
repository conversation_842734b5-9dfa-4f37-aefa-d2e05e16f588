using System.Data;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using Npgsql;
using TenantDB.Core.Connection;

namespace TenantDB.Core.DataLayer;

/// <summary>
/// PostgreSQL implementation of the data layer
/// </summary>
public class PostgreSqlDataLayer : IDataLayer
{
    private readonly IConnectionFactory _connectionFactory;
    private readonly ILogger<PostgreSqlDataLayer> _logger;

    public PostgreSqlDataLayer(
        IConnectionFactory connectionFactory,
        ILogger<PostgreSqlDataLayer> logger)
    {
        _connectionFactory = connectionFactory ?? throw new ArgumentNullException(nameof(connectionFactory));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <inheritdoc />
    public async Task<int> ExecuteNonQueryAsync(string sql, object? parameters = null, string? databaseName = null)
    {
        if (string.IsNullOrWhiteSpace(sql))
            throw new ArgumentException("SQL cannot be null or empty", nameof(sql));

        using var connection = await GetConnectionAsync(databaseName);
        using var command = connection.CreateCommand();
        command.CommandText = sql;
        
        AddParameters(command, parameters);
        
        _logger.LogDebug("Executing non-query: {Sql}", sql);
        var result = await ((NpgsqlCommand)command).ExecuteNonQueryAsync();
        _logger.LogDebug("Non-query executed, rows affected: {RowsAffected}", result);
        
        return result;
    }

    /// <inheritdoc />
    public async Task<T?> ExecuteScalarAsync<T>(string sql, object? parameters = null, string? databaseName = null)
    {
        if (string.IsNullOrWhiteSpace(sql))
            throw new ArgumentException("SQL cannot be null or empty", nameof(sql));

        using var connection = await GetConnectionAsync(databaseName);
        using var command = connection.CreateCommand();
        command.CommandText = sql;
        
        AddParameters(command, parameters);
        
        _logger.LogDebug("Executing scalar query: {Sql}", sql);
        var result = await ((NpgsqlCommand)command).ExecuteScalarAsync();
        
        if (result == null || result == DBNull.Value)
            return default(T);
            
        return (T)Convert.ChangeType(result, typeof(T));
    }

    /// <inheritdoc />
    public async Task<IEnumerable<T>> QueryAsync<T>(string sql, object? parameters = null, string? databaseName = null)
    {
        if (string.IsNullOrWhiteSpace(sql))
            throw new ArgumentException("SQL cannot be null or empty", nameof(sql));

        using var connection = await GetConnectionAsync(databaseName);
        using var command = connection.CreateCommand();
        command.CommandText = sql;
        
        AddParameters(command, parameters);
        
        _logger.LogDebug("Executing query: {Sql}", sql);
        using var reader = await ((NpgsqlCommand)command).ExecuteReaderAsync();
        
        var results = new List<T>();
        while (await reader.ReadAsync())
        {
            results.Add(MapReaderToObject<T>(reader));
        }
        
        _logger.LogDebug("Query executed, returned {Count} rows", results.Count);
        return results;
    }

    /// <inheritdoc />
    public async Task<T?> QuerySingleOrDefaultAsync<T>(string sql, object? parameters = null, string? databaseName = null)
    {
        var results = await QueryAsync<T>(sql, parameters, databaseName);
        return results.FirstOrDefault();
    }

    /// <inheritdoc />
    public async Task<bool> DatabaseExistsAsync(string databaseName)
    {
        if (string.IsNullOrWhiteSpace(databaseName))
            throw new ArgumentException("Database name cannot be null or empty", nameof(databaseName));

        const string sql = "SELECT 1 FROM pg_database WHERE datname = @databaseName";
        var result = await ExecuteScalarAsync<int?>(sql, new { databaseName }, "postgres");
        return result.HasValue;
    }

    /// <inheritdoc />
    public async Task<bool> CreateDatabaseAsync(string databaseName)
    {
        if (string.IsNullOrWhiteSpace(databaseName))
            throw new ArgumentException("Database name cannot be null or empty", nameof(databaseName));

        try
        {
            // PostgreSQL doesn't allow parameterized database names in CREATE DATABASE
            var sql = $"CREATE DATABASE \"{databaseName}\"";
            await ExecuteNonQueryAsync(sql, null, "postgres");
            _logger.LogInformation("Database {DatabaseName} created successfully", databaseName);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create database {DatabaseName}", databaseName);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<bool> DropDatabaseAsync(string databaseName)
    {
        if (string.IsNullOrWhiteSpace(databaseName))
            throw new ArgumentException("Database name cannot be null or empty", nameof(databaseName));

        try
        {
            // PostgreSQL doesn't allow parameterized database names in DROP DATABASE
            var sql = $"DROP DATABASE IF EXISTS \"{databaseName}\"";
            await ExecuteNonQueryAsync(sql, null, "postgres");
            _logger.LogInformation("Database {DatabaseName} dropped successfully", databaseName);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to drop database {DatabaseName}", databaseName);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<bool> ExecuteTransactionAsync(IEnumerable<string> sqlCommands, string? databaseName = null)
    {
        if (sqlCommands == null || !sqlCommands.Any())
            throw new ArgumentException("SQL commands cannot be null or empty", nameof(sqlCommands));

        using var connection = await GetConnectionAsync(databaseName);
        using var transaction = connection.BeginTransaction();
        
        try
        {
            foreach (var sql in sqlCommands)
            {
                if (string.IsNullOrWhiteSpace(sql)) continue;
                
                using var command = connection.CreateCommand();
                command.CommandText = sql;
                command.Transaction = transaction;
                
                _logger.LogDebug("Executing transaction command: {Sql}", sql);
                await ((NpgsqlCommand)command).ExecuteNonQueryAsync();
            }
            
            transaction.Commit();
            _logger.LogDebug("Transaction committed successfully");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Transaction failed, rolling back");
            transaction.Rollback();
            return false;
        }
    }

    private async Task<IDbConnection> GetConnectionAsync(string? databaseName)
    {
        return string.IsNullOrWhiteSpace(databaseName) 
            ? await _connectionFactory.CreateConnectionAsync()
            : await _connectionFactory.CreateConnectionAsync(databaseName);
    }

    private static void AddParameters(IDbCommand command, object? parameters)
    {
        if (parameters == null) return;

        var properties = parameters.GetType().GetProperties();
        foreach (var property in properties)
        {
            var parameter = ((NpgsqlCommand)command).CreateParameter();
            parameter.ParameterName = $"@{property.Name}";
            parameter.Value = property.GetValue(parameters) ?? DBNull.Value;
            command.Parameters.Add(parameter);
        }
    }

    private static T MapReaderToObject<T>(IDataReader reader)
    {
        // Simple mapping for basic types
        if (typeof(T).IsPrimitive || typeof(T) == typeof(string) || typeof(T) == typeof(Guid) || typeof(T) == typeof(DateTime))
        {
            return (T)Convert.ChangeType(reader[0], typeof(T));
        }

        // For complex types, create instance and map properties
        var instance = Activator.CreateInstance<T>();
        var properties = typeof(T).GetProperties();

        for (int i = 0; i < reader.FieldCount; i++)
        {
            var fieldName = reader.GetName(i);
            var property = properties.FirstOrDefault(p => 
                string.Equals(p.Name, fieldName, StringComparison.OrdinalIgnoreCase));

            if (property != null && property.CanWrite && !reader.IsDBNull(i))
            {
                var value = reader.GetValue(i);
                property.SetValue(instance, Convert.ChangeType(value, property.PropertyType));
            }
        }

        return instance;
    }
}
