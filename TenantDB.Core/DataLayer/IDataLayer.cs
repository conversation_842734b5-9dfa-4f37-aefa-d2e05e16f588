using System.Data;

namespace TenantDB.Core.DataLayer;

/// <summary>
/// Data layer interface providing database operation abstractions
/// </summary>
public interface IDataLayer
{
    /// <summary>
    /// Executes a non-query SQL command (INSERT, UPDATE, DELETE, CREATE, etc.)
    /// </summary>
    /// <param name="sql">The SQL command to execute</param>
    /// <param name="parameters">Optional parameters for the SQL command</param>
    /// <param name="databaseName">Optional specific database name to execute against</param>
    /// <returns>The number of rows affected</returns>
    Task<int> ExecuteNonQueryAsync(string sql, object? parameters = null, string? databaseName = null);

    /// <summary>
    /// Executes a SQL query and returns a scalar value
    /// </summary>
    /// <typeparam name="T">The type of the scalar value to return</typeparam>
    /// <param name="sql">The SQL query to execute</param>
    /// <param name="parameters">Optional parameters for the SQL query</param>
    /// <param name="databaseName">Optional specific database name to execute against</param>
    /// <returns>The scalar value result</returns>
    Task<T?> ExecuteScalarAsync<T>(string sql, object? parameters = null, string? databaseName = null);

    /// <summary>
    /// Executes a SQL query and returns multiple rows
    /// </summary>
    /// <typeparam name="T">The type of objects to return</typeparam>
    /// <param name="sql">The SQL query to execute</param>
    /// <param name="parameters">Optional parameters for the SQL query</param>
    /// <param name="databaseName">Optional specific database name to execute against</param>
    /// <returns>A collection of objects of type T</returns>
    Task<IEnumerable<T>> QueryAsync<T>(string sql, object? parameters = null, string? databaseName = null);

    /// <summary>
    /// Executes a SQL query and returns a single row
    /// </summary>
    /// <typeparam name="T">The type of object to return</typeparam>
    /// <param name="sql">The SQL query to execute</param>
    /// <param name="parameters">Optional parameters for the SQL query</param>
    /// <param name="databaseName">Optional specific database name to execute against</param>
    /// <returns>A single object of type T, or null if not found</returns>
    Task<T?> QuerySingleOrDefaultAsync<T>(string sql, object? parameters = null, string? databaseName = null);

    /// <summary>
    /// Checks if a database exists
    /// </summary>
    /// <param name="databaseName">The name of the database to check</param>
    /// <returns>True if the database exists, false otherwise</returns>
    Task<bool> DatabaseExistsAsync(string databaseName);

    /// <summary>
    /// Creates a new database
    /// </summary>
    /// <param name="databaseName">The name of the database to create</param>
    /// <returns>True if the database was created successfully</returns>
    Task<bool> CreateDatabaseAsync(string databaseName);

    /// <summary>
    /// Drops a database
    /// </summary>
    /// <param name="databaseName">The name of the database to drop</param>
    /// <returns>True if the database was dropped successfully</returns>
    Task<bool> DropDatabaseAsync(string databaseName);

    /// <summary>
    /// Executes multiple SQL commands within a transaction
    /// </summary>
    /// <param name="sqlCommands">The SQL commands to execute</param>
    /// <param name="databaseName">Optional specific database name to execute against</param>
    /// <returns>True if all commands executed successfully</returns>
    Task<bool> ExecuteTransactionAsync(IEnumerable<string> sqlCommands, string? databaseName = null);
}
