namespace TenantDB.Core.QueryObjects;

/// <summary>
/// Interface for executing query objects
/// </summary>
public interface IQueryExecutor
{
    /// <summary>
    /// Executes a query object that returns data
    /// </summary>
    /// <typeparam name="TResult">The type of data returned</typeparam>
    /// <param name="queryObject">The query object to execute</param>
    /// <param name="databaseName">Optional specific database name to execute against</param>
    /// <returns>A collection of results</returns>
    Task<IEnumerable<TResult>> ExecuteQueryAsync<TResult>(IQueryObject<TResult> queryObject, string? databaseName = null);

    /// <summary>
    /// Executes a query object that returns a single result
    /// </summary>
    /// <typeparam name="TResult">The type of data returned</typeparam>
    /// <param name="queryObject">The query object to execute</param>
    /// <param name="databaseName">Optional specific database name to execute against</param>
    /// <returns>A single result or null</returns>
    Task<TResult?> ExecuteQuerySingleAsync<TResult>(IQueryObject<TResult> queryObject, string? databaseName = null);

    /// <summary>
    /// Executes a command object that doesn't return data
    /// </summary>
    /// <param name="commandObject">The command object to execute</param>
    /// <param name="databaseName">Optional specific database name to execute against</param>
    /// <returns>The number of rows affected</returns>
    Task<int> ExecuteCommandAsync(ICommandObject commandObject, string? databaseName = null);

    /// <summary>
    /// Executes a query object that returns a scalar value
    /// </summary>
    /// <typeparam name="TResult">The type of scalar value returned</typeparam>
    /// <param name="queryObject">The query object to execute</param>
    /// <param name="databaseName">Optional specific database name to execute against</param>
    /// <returns>The scalar result</returns>
    Task<TResult?> ExecuteScalarAsync<TResult>(IQueryObject<TResult> queryObject, string? databaseName = null);
}
