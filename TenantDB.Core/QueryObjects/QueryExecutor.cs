using Microsoft.Extensions.Logging;
using TenantDB.Core.DataLayer;

namespace TenantDB.Core.QueryObjects;

/// <summary>
/// Default implementation of query executor
/// </summary>
public class QueryExecutor : IQueryExecutor
{
    private readonly IDataLayer _dataLayer;
    private readonly ILogger<QueryExecutor> _logger;

    public QueryExecutor(IDataLayer dataLayer, ILogger<QueryExecutor> logger)
    {
        _dataLayer = dataLayer ?? throw new ArgumentNullException(nameof(dataLayer));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <inheritdoc />
    public async Task<IEnumerable<TResult>> ExecuteQueryAsync<TResult>(IQueryObject<TResult> queryObject, string? databaseName = null)
    {
        if (queryObject == null)
            throw new ArgumentNullException(nameof(queryObject));

        _logger.LogDebug("Executing query object: {QueryType}", queryObject.GetType().Name);
        return await _dataLayer.QueryAsync<TResult>(queryObject.Sql, queryObject.Parameters, databaseName);
    }

    /// <inheritdoc />
    public async Task<TResult?> ExecuteQuerySingleAsync<TResult>(IQueryObject<TResult> queryObject, string? databaseName = null)
    {
        if (queryObject == null)
            throw new ArgumentNullException(nameof(queryObject));

        _logger.LogDebug("Executing single query object: {QueryType}", queryObject.GetType().Name);
        return await _dataLayer.QuerySingleOrDefaultAsync<TResult>(queryObject.Sql, queryObject.Parameters, databaseName);
    }

    /// <inheritdoc />
    public async Task<int> ExecuteCommandAsync(ICommandObject commandObject, string? databaseName = null)
    {
        if (commandObject == null)
            throw new ArgumentNullException(nameof(commandObject));

        _logger.LogDebug("Executing command object: {CommandType}", commandObject.GetType().Name);
        return await _dataLayer.ExecuteNonQueryAsync(commandObject.Sql, commandObject.Parameters, databaseName);
    }

    /// <inheritdoc />
    public async Task<TResult?> ExecuteScalarAsync<TResult>(IQueryObject<TResult> queryObject, string? databaseName = null)
    {
        if (queryObject == null)
            throw new ArgumentNullException(nameof(queryObject));

        _logger.LogDebug("Executing scalar query object: {QueryType}", queryObject.GetType().Name);
        return await _dataLayer.ExecuteScalarAsync<TResult>(queryObject.Sql, queryObject.Parameters, databaseName);
    }
}
