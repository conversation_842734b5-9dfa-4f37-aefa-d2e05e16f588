namespace TenantDB.Core.QueryObjects.DatabaseQueries;

/// <summary>
/// Query object to check if a database exists
/// </summary>
public class CheckDatabaseExistsQuery : IQueryObject<bool>
{
    public string Sql => "SELECT EXISTS(SELECT 1 FROM pg_database WHERE datname = @databaseName)";
    
    public object Parameters { get; }

    public CheckDatabaseExistsQuery(string databaseName)
    {
        if (string.IsNullOrWhiteSpace(databaseName))
            throw new ArgumentException("Database name cannot be null or empty", nameof(databaseName));
            
        Parameters = new { databaseName };
    }
}
