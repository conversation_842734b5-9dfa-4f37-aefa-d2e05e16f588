namespace TenantDB.Core.QueryObjects.DatabaseQueries;

/// <summary>
/// Command object to create a new database
/// </summary>
public class CreateDatabaseCommand : ICommandObject
{
    public string Sql { get; }
    
    public object? Parameters => null; // Database names cannot be parameterized in PostgreSQL

    public CreateDatabaseCommand(string databaseName)
    {
        if (string.IsNullOrWhiteSpace(databaseName))
            throw new ArgumentException("Database name cannot be null or empty", nameof(databaseName));
            
        // Note: PostgreSQL doesn't allow parameterized database names in CREATE DATABASE
        // We need to validate the database name to prevent SQL injection
        ValidateDatabaseName(databaseName);
        Sql = $"CREATE DATABASE \"{databaseName}\"";
    }

    private static void ValidateDatabaseName(string databaseName)
    {
        // Basic validation to prevent SQL injection
        if (databaseName.Contains('"') || databaseName.Contains(';') || databaseName.Contains('\''))
        {
            throw new ArgumentException("Database name contains invalid characters", nameof(databaseName));
        }
        
        if (databaseName.Length > 63) // PostgreSQL limit
        {
            throw new ArgumentException("Database name is too long (max 63 characters)", nameof(databaseName));
        }
    }
}
