namespace TenantDB.Core.QueryObjects;

/// <summary>
/// Base interface for query objects that encapsulate SQL operations
/// </summary>
public interface IQueryObject
{
    /// <summary>
    /// Gets the SQL command text
    /// </summary>
    string Sql { get; }
    
    /// <summary>
    /// Gets the parameters for the SQL command
    /// </summary>
    object? Parameters { get; }
}

/// <summary>
/// Query object interface for operations that return data
/// </summary>
/// <typeparam name="TResult">The type of data returned by the query</typeparam>
public interface IQueryObject<TResult> : IQueryObject
{
}

/// <summary>
/// Query object interface for operations that don't return data (INSERT, UPDATE, DELETE, etc.)
/// </summary>
public interface ICommandObject : IQueryObject
{
}
