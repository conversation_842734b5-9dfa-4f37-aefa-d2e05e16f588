using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using TenantDB.Core.Configuration;
using TenantDB.Core.Extensions;
using TenantDB.Core.Services;

namespace TenantDB.Example;

class Program
{
    static async Task Main(string[] args)
    {
        // Set up dependency injection
        var services = new ServiceCollection();
        
        // Add logging
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Information));
        
        // Configure database settings (you would typically load this from appsettings.json)
        var databaseConfig = new DatabaseConfiguration
        {
            Host = "localhost",
            Port = 5433, // Custom port as specified in docker-compose
            Database = "tenantdb", // This is the default database for admin operations
            Username = "tenantdb_user",
            Password = "tenantdb_password"
        };
        
        // Register TenantDB services
        services.AddTenantDB(databaseConfig, "sql");
        
        // Build service provider
        var serviceProvider = services.BuildServiceProvider();
        
        // Get the tenant database setup service
        var tenantSetupService = serviceProvider.GetRequiredService<ITenantDatabaseSetupService>();
        var logger = serviceProvider.GetRequiredService<ILogger<Program>>();
        
        try
        {
            logger.LogInformation("TenantDB Framework Example");
            logger.LogInformation("========================");
            
            // Example tenant ID
            const string tenantId = "example-tenant-001";
            
            // Check if tenant database already exists
            logger.LogInformation("Checking if tenant database exists for tenant: {TenantId}", tenantId);
            var exists = await tenantSetupService.TenantDatabaseExistsAsync(tenantId);
            
            if (exists)
            {
                logger.LogInformation("Tenant database already exists. Removing it first...");
                var removed = await tenantSetupService.RemoveTenantDatabaseAsync(tenantId);
                if (!removed)
                {
                    logger.LogError("Failed to remove existing tenant database");
                    return;
                }
            }
            
            // Set up the tenant database
            logger.LogInformation("Setting up tenant database for tenant: {TenantId}", tenantId);
            var success = await tenantSetupService.SetupTenantDatabaseAsync(tenantId);
            
            if (success)
            {
                logger.LogInformation("✅ Tenant database setup completed successfully!");
                logger.LogInformation("Database name: {DatabaseName}", tenantSetupService.GetTenantDatabaseName(tenantId));
                
                // Verify the database exists
                var verifyExists = await tenantSetupService.TenantDatabaseExistsAsync(tenantId);
                logger.LogInformation("Database exists verification: {Exists}", verifyExists);
            }
            else
            {
                logger.LogError("❌ Failed to set up tenant database");
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while running the example");
        }
        finally
        {
            await serviceProvider.DisposeAsync();
        }
        
        logger.LogInformation("Example completed. Press any key to exit...");
        Console.ReadKey();
    }
}
