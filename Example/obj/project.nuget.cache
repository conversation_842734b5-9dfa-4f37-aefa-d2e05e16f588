{"version": 2, "dgSpecHash": "2Zqr3xbZM4c=", "success": true, "projectFilePath": "/home/<USER>/projects/whatson2/tenantdb/Example/Example.csproj", "expectedPackageFiles": ["/home/<USER>/.nuget/packages/microsoft.extensions.configuration/9.0.8/microsoft.extensions.configuration.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.configuration.abstractions/9.0.8/microsoft.extensions.configuration.abstractions.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.configuration.binder/9.0.8/microsoft.extensions.configuration.binder.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection/9.0.8/microsoft.extensions.dependencyinjection.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection.abstractions/9.0.8/microsoft.extensions.dependencyinjection.abstractions.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.logging/9.0.8/microsoft.extensions.logging.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.logging.abstractions/9.0.8/microsoft.extensions.logging.abstractions.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.logging.configuration/9.0.8/microsoft.extensions.logging.configuration.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.logging.console/9.0.8/microsoft.extensions.logging.console.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.options/9.0.8/microsoft.extensions.options.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.options.configurationextensions/9.0.8/microsoft.extensions.options.configurationextensions.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.primitives/9.0.8/microsoft.extensions.primitives.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/npgsql/9.0.3/npgsql.9.0.3.nupkg.sha512", "/home/<USER>/.nuget/packages/system.text.json/9.0.7/system.text.json.9.0.7.nupkg.sha512"], "logs": []}