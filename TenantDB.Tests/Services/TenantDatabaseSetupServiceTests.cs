using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using TenantDB.Core.DataLayer;
using TenantDB.Core.QueryObjects;
using TenantDB.Core.QueryObjects.DatabaseQueries;
using TenantDB.Core.Services;

namespace TenantDB.Tests.Services;

public class TenantDatabaseSetupServiceTests
{
    private readonly Mock<IDataLayer> _mockDataLayer;
    private readonly Mock<IQueryExecutor> _mockQueryExecutor;
    private readonly Mock<ILogger<TenantDatabaseSetupService>> _mockLogger;
    private readonly TenantDatabaseSetupService _service;

    public TenantDatabaseSetupServiceTests()
    {
        _mockDataLayer = new Mock<IDataLayer>();
        _mockQueryExecutor = new Mock<IQueryExecutor>();
        _mockLogger = new Mock<ILogger<TenantDatabaseSetupService>>();
        
        _service = new TenantDatabaseSetupService(
            _mockDataLayer.Object,
            _mockQueryExecutor.Object,
            _mockLogger.Object,
            "test-sql-path");
    }

    [Fact]
    public void GetTenantDatabaseName_WithValidTenantId_ShouldReturnCorrectDatabaseName()
    {
        // Arrange
        const string tenantId = "test-tenant-123";

        // Act
        var databaseName = _service.GetTenantDatabaseName(tenantId);

        // Assert
        databaseName.Should().Be("tenant_test_tenant_123");
    }

    [Fact]
    public void GetTenantDatabaseName_WithTenantIdContainingSpaces_ShouldReplaceSpacesWithUnderscores()
    {
        // Arrange
        const string tenantId = "test tenant 123";

        // Act
        var databaseName = _service.GetTenantDatabaseName(tenantId);

        // Assert
        databaseName.Should().Be("tenant_test_tenant_123");
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public void GetTenantDatabaseName_WithInvalidTenantId_ShouldThrowArgumentException(string? tenantId)
    {
        // Act & Assert
        var act = () => _service.GetTenantDatabaseName(tenantId!);
        act.Should().Throw<ArgumentException>()
            .WithMessage("Tenant ID cannot be null or empty*");
    }

    [Fact]
    public async Task TenantDatabaseExistsAsync_WhenDatabaseExists_ShouldReturnTrue()
    {
        // Arrange
        const string tenantId = "test-tenant";
        _mockQueryExecutor
            .Setup(x => x.ExecuteScalarAsync<bool>(It.IsAny<CheckDatabaseExistsQuery>(), "postgres"))
            .ReturnsAsync(true);

        // Act
        var result = await _service.TenantDatabaseExistsAsync(tenantId);

        // Assert
        result.Should().BeTrue();
        _mockQueryExecutor.Verify(
            x => x.ExecuteScalarAsync<bool>(It.IsAny<CheckDatabaseExistsQuery>(), "postgres"),
            Times.Once);
    }

    [Fact]
    public async Task TenantDatabaseExistsAsync_WhenDatabaseDoesNotExist_ShouldReturnFalse()
    {
        // Arrange
        const string tenantId = "test-tenant";
        _mockQueryExecutor
            .Setup(x => x.ExecuteScalarAsync<bool>(It.IsAny<CheckDatabaseExistsQuery>(), "postgres"))
            .ReturnsAsync(false);

        // Act
        var result = await _service.TenantDatabaseExistsAsync(tenantId);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task TenantDatabaseExistsAsync_WhenExceptionOccurs_ShouldReturnFalse()
    {
        // Arrange
        const string tenantId = "test-tenant";
        _mockQueryExecutor
            .Setup(x => x.ExecuteScalarAsync<bool>(It.IsAny<CheckDatabaseExistsQuery>(), "postgres"))
            .ThrowsAsync(new Exception("Database error"));

        // Act
        var result = await _service.TenantDatabaseExistsAsync(tenantId);

        // Assert
        result.Should().BeFalse();
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public async Task TenantDatabaseExistsAsync_WithInvalidTenantId_ShouldThrowArgumentException(string? tenantId)
    {
        // Act & Assert
        var act = async () => await _service.TenantDatabaseExistsAsync(tenantId!);
        await act.Should().ThrowAsync<ArgumentException>()
            .WithMessage("Tenant ID cannot be null or empty*");
    }

    [Fact]
    public async Task RemoveTenantDatabaseAsync_WhenSuccessful_ShouldReturnTrue()
    {
        // Arrange
        const string tenantId = "test-tenant";
        _mockDataLayer
            .Setup(x => x.DropDatabaseAsync(It.IsAny<string>()))
            .ReturnsAsync(true);

        // Act
        var result = await _service.RemoveTenantDatabaseAsync(tenantId);

        // Assert
        result.Should().BeTrue();
        _mockDataLayer.Verify(
            x => x.DropDatabaseAsync("tenant_test_tenant"),
            Times.Once);
    }

    [Fact]
    public async Task RemoveTenantDatabaseAsync_WhenFailed_ShouldReturnFalse()
    {
        // Arrange
        const string tenantId = "test-tenant";
        _mockDataLayer
            .Setup(x => x.DropDatabaseAsync(It.IsAny<string>()))
            .ReturnsAsync(false);

        // Act
        var result = await _service.RemoveTenantDatabaseAsync(tenantId);

        // Assert
        result.Should().BeFalse();
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public async Task RemoveTenantDatabaseAsync_WithInvalidTenantId_ShouldThrowArgumentException(string? tenantId)
    {
        // Act & Assert
        var act = async () => await _service.RemoveTenantDatabaseAsync(tenantId!);
        await act.Should().ThrowAsync<ArgumentException>()
            .WithMessage("Tenant ID cannot be null or empty*");
    }
}
