using FluentAssertions;
using TenantDB.Core.QueryObjects.DatabaseQueries;

namespace TenantDB.Tests.QueryObjects;

public class DatabaseQueriesTests
{
    [Fact]
    public void CheckDatabaseExistsQuery_WithValidDatabaseName_ShouldCreateCorrectQuery()
    {
        // Arrange
        const string databaseName = "test_database";

        // Act
        var query = new CheckDatabaseExistsQuery(databaseName);

        // Assert
        query.Sql.Should().Be("SELECT EXISTS(SELECT 1 FROM pg_database WHERE datname = @databaseName)");
        query.Parameters.Should().NotBeNull();

        // Use reflection to check the anonymous object property
        var parametersType = query.Parameters!.GetType();
        var databaseNameProperty = parametersType.GetProperty("databaseName");
        databaseNameProperty.Should().NotBeNull();
        var actualDatabaseName = databaseNameProperty!.GetValue(query.Parameters) as string;
        actualDatabaseName.Should().Be(databaseName);
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public void CheckDatabaseExistsQuery_WithInvalidDatabaseName_ShouldThrowArgumentException(string? databaseName)
    {
        // Act & Assert
        var act = () => new CheckDatabaseExistsQuery(databaseName!);
        act.Should().Throw<ArgumentException>()
            .WithMessage("Database name cannot be null or empty*");
    }

    [Fact]
    public void CreateDatabaseCommand_WithValidDatabaseName_ShouldCreateCorrectCommand()
    {
        // Arrange
        const string databaseName = "test_database";

        // Act
        var command = new CreateDatabaseCommand(databaseName);

        // Assert
        command.Sql.Should().Be("CREATE DATABASE \"test_database\"");
        command.Parameters.Should().BeNull();
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public void CreateDatabaseCommand_WithInvalidDatabaseName_ShouldThrowArgumentException(string? databaseName)
    {
        // Act & Assert
        var act = () => new CreateDatabaseCommand(databaseName!);
        act.Should().Throw<ArgumentException>()
            .WithMessage("Database name cannot be null or empty*");
    }

    [Theory]
    [InlineData("test\"database")]
    [InlineData("test;database")]
    [InlineData("test'database")]
    public void CreateDatabaseCommand_WithDangerousCharacters_ShouldThrowArgumentException(string databaseName)
    {
        // Act & Assert
        var act = () => new CreateDatabaseCommand(databaseName);
        act.Should().Throw<ArgumentException>()
            .WithMessage("Database name contains invalid characters*");
    }

    [Fact]
    public void CreateDatabaseCommand_WithTooLongDatabaseName_ShouldThrowArgumentException()
    {
        // Arrange
        var longDatabaseName = new string('a', 64); // PostgreSQL limit is 63

        // Act & Assert
        var act = () => new CreateDatabaseCommand(longDatabaseName);
        act.Should().Throw<ArgumentException>()
            .WithMessage("Database name is too long*");
    }
}
