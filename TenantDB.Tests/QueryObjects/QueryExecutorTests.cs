using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using TenantDB.Core.DataLayer;
using TenantDB.Core.QueryObjects;

namespace TenantDB.Tests.QueryObjects;

public class QueryExecutorTests
{
    private readonly Mock<IDataLayer> _mockDataLayer;
    private readonly Mock<ILogger<QueryExecutor>> _mockLogger;
    private readonly QueryExecutor _queryExecutor;

    public QueryExecutorTests()
    {
        _mockDataLayer = new Mock<IDataLayer>();
        _mockLogger = new Mock<ILogger<QueryExecutor>>();
        _queryExecutor = new QueryExecutor(_mockDataLayer.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task ExecuteQueryAsync_WithValidQueryObject_ShouldCallDataLayerQuery()
    {
        // Arrange
        var mockQuery = new Mock<IQueryObject<string>>();
        mockQuery.Setup(x => x.Sql).Returns("SELECT * FROM test");
        mockQuery.Setup(x => x.Parameters).Returns(new { id = 1 });

        var expectedResults = new[] { "result1", "result2" };
        _mockDataLayer
            .Setup(x => x.QueryAsync<string>("SELECT * FROM test", It.IsAny<object>(), null))
            .ReturnsAsync(expectedResults);

        // Act
        var results = await _queryExecutor.ExecuteQueryAsync(mockQuery.Object);

        // Assert
        results.Should().BeEquivalentTo(expectedResults);
        _mockDataLayer.Verify(
            x => x.QueryAsync<string>("SELECT * FROM test", It.IsAny<object>(), null),
            Times.Once);
    }

    [Fact]
    public async Task ExecuteQueryAsync_WithNullQueryObject_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        var act = async () => await _queryExecutor.ExecuteQueryAsync<string>(null!);
        await act.Should().ThrowAsync<ArgumentNullException>();
    }

    [Fact]
    public async Task ExecuteQuerySingleAsync_WithValidQueryObject_ShouldCallDataLayerQuerySingle()
    {
        // Arrange
        var mockQuery = new Mock<IQueryObject<string>>();
        mockQuery.Setup(x => x.Sql).Returns("SELECT * FROM test WHERE id = @id");
        mockQuery.Setup(x => x.Parameters).Returns(new { id = 1 });

        const string expectedResult = "single_result";
        _mockDataLayer
            .Setup(x => x.QuerySingleOrDefaultAsync<string>("SELECT * FROM test WHERE id = @id", It.IsAny<object>(), null))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _queryExecutor.ExecuteQuerySingleAsync(mockQuery.Object);

        // Assert
        result.Should().Be(expectedResult);
        _mockDataLayer.Verify(
            x => x.QuerySingleOrDefaultAsync<string>("SELECT * FROM test WHERE id = @id", It.IsAny<object>(), null),
            Times.Once);
    }

    [Fact]
    public async Task ExecuteCommandAsync_WithValidCommandObject_ShouldCallDataLayerExecuteNonQuery()
    {
        // Arrange
        var mockCommand = new Mock<ICommandObject>();
        mockCommand.Setup(x => x.Sql).Returns("INSERT INTO test (name) VALUES (@name)");
        mockCommand.Setup(x => x.Parameters).Returns(new { name = "test" });

        const int expectedRowsAffected = 1;
        _mockDataLayer
            .Setup(x => x.ExecuteNonQueryAsync("INSERT INTO test (name) VALUES (@name)", It.IsAny<object>(), null))
            .ReturnsAsync(expectedRowsAffected);

        // Act
        var result = await _queryExecutor.ExecuteCommandAsync(mockCommand.Object);

        // Assert
        result.Should().Be(expectedRowsAffected);
        _mockDataLayer.Verify(
            x => x.ExecuteNonQueryAsync("INSERT INTO test (name) VALUES (@name)", It.IsAny<object>(), null),
            Times.Once);
    }

    [Fact]
    public async Task ExecuteScalarAsync_WithValidQueryObject_ShouldCallDataLayerExecuteScalar()
    {
        // Arrange
        var mockQuery = new Mock<IQueryObject<int>>();
        mockQuery.Setup(x => x.Sql).Returns("SELECT COUNT(*) FROM test");
        mockQuery.Setup(x => x.Parameters).Returns(null);

        const int expectedResult = 42;
        _mockDataLayer
            .Setup(x => x.ExecuteScalarAsync<int>("SELECT COUNT(*) FROM test", null, null))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _queryExecutor.ExecuteScalarAsync(mockQuery.Object);

        // Assert
        result.Should().Be(expectedResult);
        _mockDataLayer.Verify(
            x => x.ExecuteScalarAsync<int>("SELECT COUNT(*) FROM test", null, null),
            Times.Once);
    }

    [Fact]
    public async Task ExecuteQueryAsync_WithSpecificDatabase_ShouldPassDatabaseNameToDataLayer()
    {
        // Arrange
        var mockQuery = new Mock<IQueryObject<string>>();
        mockQuery.Setup(x => x.Sql).Returns("SELECT * FROM test");
        mockQuery.Setup(x => x.Parameters).Returns(null);

        const string databaseName = "specific_database";
        var expectedResults = new[] { "result1" };
        _mockDataLayer
            .Setup(x => x.QueryAsync<string>("SELECT * FROM test", null, databaseName))
            .ReturnsAsync(expectedResults);

        // Act
        var results = await _queryExecutor.ExecuteQueryAsync(mockQuery.Object, databaseName);

        // Assert
        results.Should().BeEquivalentTo(expectedResults);
        _mockDataLayer.Verify(
            x => x.QueryAsync<string>("SELECT * FROM test", null, databaseName),
            Times.Once);
    }
}
