{"format": 1, "restore": {"/home/<USER>/projects/whatson2/tenantdb/TenantDB.Tests/TenantDB.Tests.csproj": {}}, "projects": {"/home/<USER>/projects/whatson2/tenantdb/TenantDB.Core/TenantDB.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/home/<USER>/projects/whatson2/tenantdb/TenantDB.Core/TenantDB.Core.csproj", "projectName": "TenantDB.Core", "projectPath": "/home/<USER>/projects/whatson2/tenantdb/TenantDB.Core/TenantDB.Core.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/home/<USER>/projects/whatson2/tenantdb/TenantDB.Core/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"/var/snap/dotnet/common/dotnet/library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Dapper": {"target": "Package", "version": "[2.1.66, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.8, )"}, "Microsoft.Extensions.Configuration.Binder": {"target": "Package", "version": "[9.0.8, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.7, )"}, "Npgsql": {"target": "Package", "version": "[9.0.3, )"}, "System.Text.Json": {"target": "Package", "version": "[9.0.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/var/snap/dotnet/common/dotnet/sdk/9.0.108/PortableRuntimeIdentifierGraph.json"}}}, "/home/<USER>/projects/whatson2/tenantdb/TenantDB.Tests/TenantDB.Tests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/home/<USER>/projects/whatson2/tenantdb/TenantDB.Tests/TenantDB.Tests.csproj", "projectName": "TenantDB.Tests", "projectPath": "/home/<USER>/projects/whatson2/tenantdb/TenantDB.Tests/TenantDB.Tests.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/home/<USER>/projects/whatson2/tenantdb/TenantDB.Tests/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"/var/snap/dotnet/common/dotnet/library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"/home/<USER>/projects/whatson2/tenantdb/TenantDB.Core/TenantDB.Core.csproj": {"projectPath": "/home/<USER>/projects/whatson2/tenantdb/TenantDB.Core/TenantDB.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"FluentAssertions": {"target": "Package", "version": "[8.5.0, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.12.0, )"}, "Moq": {"target": "Package", "version": "[4.20.72, )"}, "coverlet.collector": {"target": "Package", "version": "[6.0.2, )"}, "xunit": {"target": "Package", "version": "[2.9.2, )"}, "xunit.runner.visualstudio": {"target": "Package", "version": "[2.8.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/var/snap/dotnet/common/dotnet/sdk/9.0.108/PortableRuntimeIdentifierGraph.json"}}}}}