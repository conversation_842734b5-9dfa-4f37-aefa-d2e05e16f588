# TenantDB Framework

A custom .NET 9 framework for managing tenant-based PostgreSQL databases with event sourcing capabilities.

## Overview

TenantDB is a lightweight framework designed to handle tenant-based database setup and management for event-sourced applications. It provides a simple data layer and query object pattern for PostgreSQL operations, focusing specifically on database creation and schema setup for multi-tenant applications.

## Features

- **Tenant Database Management**: Automatically create and manage separate databases for each tenant
- **Event Sourcing Schema**: Pre-built SQL schema for event sourcing with aggregates, events, and compliance tracking
- **Data Layer Pattern**: Clean abstraction over PostgreSQL operations
- **Query Object Pattern**: Encapsulated SQL operations for better maintainability
- **Dependency Injection**: Full support for .NET dependency injection
- **Comprehensive Testing**: Unit tests with high coverage using xUnit and FluentAssertions

## Architecture

The framework follows these design patterns:
- **Data Layer Pattern**: Abstraction for database operations (`IDataLayer`, `PostgreSqlDataLayer`)
- **Query Object Pattern**: Encapsulated SQL queries (`IQueryObject`, `ICommandObject`)
- **Service Layer**: High-level tenant management operations (`ITenantDatabaseSetupService`)

## Database Schema

The framework sets up a complete event sourcing schema including:
- **Event Sources**: Track sources of events (users, services, scrapers, etc.)
- **Base Events**: Core event store with aggregate versioning
- **Aggregates**: Current state of entities (Person, Company, Website, Place)
- **Compliance Tracking**: GDPR and audit requirements
- **Utility Functions**: Event sourcing helper functions

## Quick Start

### 1. Set up PostgreSQL with Docker

```bash
# Start PostgreSQL instance
docker-compose up -d
```

### 2. Configure Your Application

```csharp
using Microsoft.Extensions.DependencyInjection;
using TenantDB.Core.Configuration;
using TenantDB.Core.Extensions;

var services = new ServiceCollection();

// Configure database
var databaseConfig = new DatabaseConfiguration
{
    Host = "localhost",
    Port = 5433,
    Database = "tenantdb",
    Username = "tenantdb_user",
    Password = "tenantdb_password"
};

// Register TenantDB services
services.AddTenantDB(databaseConfig, "sql");
```

### 3. Use the Tenant Setup Service

```csharp
var tenantSetupService = serviceProvider.GetRequiredService<ITenantDatabaseSetupService>();

// Create a new tenant database
string tenantId = "my-tenant-001";
bool success = await tenantSetupService.SetupTenantDatabaseAsync(tenantId);

if (success)
{
    Console.WriteLine($"Tenant database created: {tenantSetupService.GetTenantDatabaseName(tenantId)}");
}
```

## Project Structure

```
TenantDB/
├── TenantDB.Core/              # Core framework library
│   ├── Configuration/          # Database configuration
│   ├── Connection/             # Connection factory
│   ├── DataLayer/              # Data access abstraction
│   ├── QueryObjects/           # Query object pattern implementation
│   ├── Services/               # High-level services
│   └── Extensions/             # Dependency injection extensions
├── TenantDB.Tests/             # Unit tests
├── Example/                    # Example console application
├── sql/                        # Database schema scripts
├── docker-compose.yml          # PostgreSQL setup
└── .env                        # Environment configuration
```

## Environment Configuration

The `.env` file contains database configuration:

```env
POSTGRES_HOST=localhost
POSTGRES_PORT=5433
POSTGRES_DB=tenantdb
POSTGRES_USER=tenantdb_user
POSTGRES_PASSWORD=tenantdb_password
```

## Running Tests

```bash
dotnet test
```

## Running the Example

```bash
cd Example
dotnet run
```

## Dependencies

- .NET 9.0
- Npgsql (PostgreSQL driver)
- Microsoft.Extensions.* (Configuration, DependencyInjection, Logging)
- xUnit, FluentAssertions, Moq (for testing)

## License

This project is intended as an internal SDK for event-based database management.