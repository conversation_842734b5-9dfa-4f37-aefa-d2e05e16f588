# TenantDB Framework

A minimal .NET 9 framework for managing tenant-based PostgreSQL databases using Dapper.

## Overview

TenantDB is a lightweight framework designed to handle tenant-based database setup and management. It provides a simple data layer using Dapper for PostgreSQL operations, focusing specifically on database creation and schema setup for multi-tenant applications.

## Features

- **Tenant Database Management**: Automatically create and manage separate databases for each tenant
- **Dapper Integration**: Fast, lightweight ORM for database operations
- **Simple Data Layer**: Clean abstraction over PostgreSQL operations using Dapper
- **Dependency Injection**: Full support for .NET dependency injection
- **Comprehensive Testing**: Unit tests with high coverage using xUnit and FluentAssertions

## Architecture

The framework follows these design patterns:
- **Data Layer Pattern**: Simple abstraction for database operations using Dapper (`IData<PERSON>ayer`, `DapperDataLayer`)
- **Service Layer**: High-level tenant management operations (`ITenantDatabaseSetupService`)
- **Connection Factory**: Manages PostgreSQL connections for different databases

## Database Schema

The framework can execute SQL scripts to set up your database schema. Place your SQL files in the `sql/` directory and they will be executed in alphabetical order when setting up a new tenant database.

## Quick Start

### 1. Set up PostgreSQL with Docker

```bash
# Start PostgreSQL instance
docker-compose up -d
```

### 2. Configure Your Application

```csharp
using Microsoft.Extensions.DependencyInjection;
using TenantDB.Core.Configuration;
using TenantDB.Core.Extensions;

var services = new ServiceCollection();

// Configure database
var databaseConfig = new DatabaseConfiguration
{
    Host = "localhost",
    Port = 5433,
    Database = "tenantdb",
    Username = "tenantdb_user",
    Password = "tenantdb_password"
};

// Register TenantDB services
services.AddTenantDB(databaseConfig, "sql");
```

### 3. Use the Tenant Setup Service

```csharp
var tenantSetupService = serviceProvider.GetRequiredService<ITenantDatabaseSetupService>();

// Create a new tenant database
string tenantId = "my-tenant-001";
bool success = await tenantSetupService.SetupTenantDatabaseAsync(tenantId);

if (success)
{
    Console.WriteLine($"Tenant database created: {tenantSetupService.GetTenantDatabaseName(tenantId)}");
}
```

### 4. Use Dapper Directly

```csharp
using Dapper;
using TenantDB.Core.DataLayer;

// Get a connection to a specific tenant database
var dataLayer = serviceProvider.GetRequiredService<IDataLayer>();
using var connection = await dataLayer.GetConnectionAsync("tenant_my_tenant_001");

// Use Dapper to query the database
var results = await connection.QueryAsync<MyEntity>("SELECT * FROM my_table WHERE id = @id", new { id = 123 });
```

## Project Structure

```
TenantDB/
├── TenantDB.Core/              # Core framework library
│   ├── Configuration/          # Database configuration
│   ├── Connection/             # Connection factory
│   ├── DataLayer/              # Dapper-based data access layer
│   ├── Services/               # High-level tenant management services
│   └── Extensions/             # Dependency injection extensions
├── TenantDB.Tests/             # Unit tests
├── Example/                    # Example console application
├── sql/                        # Database schema scripts
├── docker-compose.yml          # PostgreSQL setup
└── .env                        # Environment configuration
```

## Environment Configuration

The `.env` file contains database configuration:

```env
POSTGRES_HOST=localhost
POSTGRES_PORT=5433
POSTGRES_DB=tenantdb
POSTGRES_USER=tenantdb_user
POSTGRES_PASSWORD=tenantdb_password
```

## Running Tests

```bash
dotnet test
```

## Running the Example

```bash
cd Example
dotnet run
```

## Dependencies

- .NET 9.0
- Dapper (lightweight ORM)
- Npgsql (PostgreSQL driver)
- Microsoft.Extensions.* (Configuration, DependencyInjection, Logging)
- xUnit, FluentAssertions, Moq (for testing)

## License

This project is intended as an internal SDK for event-based database management.